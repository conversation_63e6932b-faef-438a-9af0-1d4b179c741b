import { Component, inject, signal, OnInit, DestroyRef } from '@angular/core';
import { ReactiveFormsModule, FormGroup, FormControl, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { debounceTime, distinctUntilChanged, filter } from 'rxjs/operators';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

export interface User {
  id: number;
  name: string;
  username: string;
  email: string;
};


@Component({
  selector: 'app-search',
  imports: [ReactiveFormsModule],
  templateUrl: './search.html',
  styleUrl: './search.css',
  standalone: true
})
export class Search implements OnInit {

  private readonly router = inject(Router);
  private readonly destroyRef = inject(DestroyRef);

  readonly searchForm = new FormGroup({
    query: new FormControl<string>('', [Validators.minLength(3), Validators.maxLength(10)])
  });

  readonly listData = signal<User[]>([]);
  readonly isLoading = signal<boolean>(false);

  constructor() {
    this.setupReactiveSearch();
  }

  get queryControl() {
    return this.searchForm.controls.query;
  }

  ngOnInit() {
    // Component initialization logic can go here if needed
  }




  private setupReactiveSearch() {
    // Mark control as touched when user starts typing
    this.queryControl.valueChanges
      .pipe(
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(() => {
        if (!this.queryControl.touched) {
          this.queryControl.markAsTouched();
        }
      });

    // Handle search logic
    this.queryControl.valueChanges
      .pipe(
        debounceTime(300), // Wait 300ms after user stops typing
        distinctUntilChanged(), // Only emit when value actually changes
        filter(value => (value?.length ?? 0) >= 2), // Only search when at least 2 characters
        takeUntilDestroyed(this.destroyRef)// Unsubscribe when component is destroyed
      )
      .subscribe(searchTerm => {
        if (searchTerm) {
          this.performSearch(searchTerm);
        } else {
          this.listData.set([]);
        }
      });
  }

  private performSearch(searchTerm: string) {
    this.isLoading.set(true);

    // Simulate API call delay
    setTimeout(() => {
      const initialData = [
        { id: 1, name: 'John Doe', username: 'john', email: '<EMAIL>' },
        { id: 2, name: 'Jane Smith', username: 'jane', email: '<EMAIL>' },
        { id: 3, name: 'Jack Johnson', username: 'jack', email: '<EMAIL>' },
        { id: 4, name: 'Jill Brown', username: 'jill', email: '<EMAIL>' },
        { id: 5, name: 'James Wilson', username: 'james', email: '<EMAIL>' }
      ];

      const filteredData = initialData.filter(item =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.email.toLowerCase().includes(searchTerm.toLowerCase())
      );

      this.listData.set(filteredData);
      this.isLoading.set(false);
    }, 200); // Simulate 200ms API response time
  }

  clear() {
    this.searchForm.reset();
    this.listData.set([]);
  }

  takePhoto() {
    console.log('Take photo functionality not implemented yet');
  }

  recordVoice() {
    console.log('Voice recording functionality not implemented yet');
  }

  search() {
    // This method is called when form is submitted
    const searchTerm = this.queryControl.value;
    if (searchTerm && searchTerm.length >= 2) {
      this.performSearch(searchTerm);
    }
  }

  navigateToProfile(userId: number) {
    this.router.navigate(['/user', userId]);

  }


  navigateToProfileWithQueryParams(userId: number) {
    this.router.navigate(['/user', userId], { queryParams: { name: 'John' } });

  }

}
