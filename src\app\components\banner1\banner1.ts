import { httpResource } from '@angular/common/http';
import { Component, input, model, output, signal } from '@angular/core';

export interface User {
  id: number;
  name: string;
  username: string;
  email: string;
};

@Component({
  selector: 'app-banner1',
  imports: [],
  templateUrl: './banner1.html',
  styleUrl: './banner1.css'
})
export class Banner1 {
  title = input.required<string>();
  description = input.required<string>();

  collapsed = model(false);

  closed = output<boolean>();

  userId = signal('1');
  user = httpResource<User>(() => `https://jsonplaceholder.typicode.com/users/${this.userId()}`);


}
