import { Component, ComponentRef, effect, inputBinding, outputBinding, twoWayBinding, signal, viewChild, ViewContainerRef } from '@angular/core';
import { Banner1 } from '@components/banner1/banner1';

@Component({
  selector: 'app-about',
  imports: [],
  template: `
    <ng-container #container></ng-container>
    <section>
      <button (click)="createComponent()">CreateComponent</button>
    </section>

  `,
  styleUrl: './about.css'
})
export class About {

  // dynamic create component demo
  vcr = viewChild('container', { read: ViewContainerRef });
  compactMode = signal(false);
  #componentRef?: ComponentRef<Banner1>;

  constructor() {
    effect(() => {
      const isCompactMode = this.compactMode();
      this.#componentRef?.setInput('collapsed', isCompactMode);
    })
  }


  createComponent() {
    this.vcr()?.clear();
    // this.#componentRef = this.vcr()?.createComponent(Banner1);
    // this.#componentRef?.setInput('title', 'banner1 new');

    // this.#componentRef?.setInput('description', 'banner1 new description');

    // this.#componentRef?.setInput('collapsed', this.compactMode());
    // this.#componentRef?.instance.collapsed.subscribe((isCollapsed) => {
    //   this.compactMode.set(isCollapsed);
    // });
    //  outputBinding<boolean>('collapsedChanged', (isCollapsed) => {
    //   this.compactMode.set(isCollapsed)
    // }),

    this.#componentRef = this.vcr()?.createComponent(Banner1, {
      bindings: [
        inputBinding('title', () => 'banner1 new'),
        inputBinding('description', () => 'banner1 new description'),
        inputBinding('collapsed', this.compactMode),
        twoWayBinding('collapsed', this.compactMode),
        outputBinding('closed', () => {
          this.#componentRef?.destroy();
          this.#componentRef = undefined;
        })
      ]
    });


    //     this.#componentRef?.instance.closed.subscribe((isClosed) => {
    //       if (isClosed) {
    //         this.vcr()?.clear();
    //       }
    //     });
  }

}
