import { Component, ComponentRef, effect, inputBinding, outputBinding, twoWayBinding, signal, viewChild, ViewContainerRef, afterNextRender, Injector } from '@angular/core';
import { Banner1 } from '@components/banner1/banner1';

@Component({
  selector: 'app-about',
  imports: [],
  template: `
    <ng-container #container></ng-container>
    <section>
      <button (click)="createComponent()">CreateComponent</button>
    </section>

  `,
  styleUrl: './about.css'
})
export class About {

  // dynamic create component demo
  vcr = viewChild('container', { read: ViewContainerRef });
  compactMode = signal(false);
  #componentRef?: ComponentRef<Banner1>;

  constructor(private injector: Injector) {
    // Use afterNextRender to ensure this runs only on the client after hydration
    afterNextRender(() => {
      effect(() => {
        const isCompactMode = this.compactMode();
        this.#componentRef?.setInput('collapsed', isCompactMode);
      });
    }, { injector: this.injector });
  }


  createComponent() {
    // Ensure this only runs on the client side after hydration
    afterNextRender(() => {
      this.vcr()?.clear();

      this.#componentRef = this.vcr()?.createComponent(Banner1, {
        bindings: [
          inputBinding('title', () => 'banner1 new'),
          inputBinding('description', () => 'banner1 new description'),
          inputBinding('collapsed', this.compactMode),
          twoWayBinding('collapsed', this.compactMode),
          outputBinding('closed', () => {
            this.#componentRef?.destroy();
            this.#componentRef = undefined;
          })
        ]
      });
    }, { injector: this.injector });
  }

}
