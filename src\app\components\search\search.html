<div class="container">
    <form [formGroup]="searchForm" role="search" (onSubmit)="search()" class="row gap-2 my-2">
        <div class="col-12 d-flex align-items-center gap-2 rounded-top shadow  px-2 py-1 div-ring"
            [class.rounded-bottom]="!(listData().length > 0 || (queryControl.value?.length ?? 0) > 0) && !queryControl.invalid">
            <i class="bi bi-search"></i>
            <input type="text" class="form-control border-0" id="query" aria-label="Search" formControlName="query"
                placeholder="Search..." [class.is-invalid]="queryControl.invalid && queryControl.touched">
            @if(searchForm.controls.query.value){
            <a href="javascript:void(0)" role="button" (click)="clear()">
                <i class="bi bi-x text-secondary fs-4"></i>
            </a>
            }
            <a href="javascript:void(0)" role="button" (click)="recordVoice()">
                <i class="bi bi-mic"></i>
            </a>
            <a href="javascript:void(0)" role="button" (click)="takePhoto()">
                <i class="bi bi-camera"></i>
            </a>

        </div>

        <!-- Validation Error Messages -->
        @if(queryControl.invalid && queryControl.touched) {
        <div class="col-12">
            <div class="alert alert-danger py-2 mb-0" role="alert">
                @if(queryControl.errors?.['minlength']) {
                <div class="d-flex align-items-center">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <small>Minimum length is {{queryControl.errors?.['minlength'].requiredLength}} characters</small>
                </div>
                }
                @if(queryControl.errors?.['maxlength']) {
                <div class="d-flex align-items-center">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <small>Maximum length is {{queryControl.errors?.['maxlength'].requiredLength}} characters</small>
                </div>
                }
            </div>
        </div>
        }
        @if(listData().length > 0 || isLoading() ||
        (queryControl.value?.length ?? 0) > 0){
        <div class="col-12 list-group list-group-flush rounded-bottom shadow">
            @if(isLoading()){
            <div class="list-group-item text-center py-3">
                <div class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></div>
                Searching...
            </div>
            } @else {
            @for(user of listData();track user.id){
            <a href="javascript:void(0)" class="list-group-item list-group-item-action " aria-current="true"
                (click)="navigateToProfileWithQueryParams(user.id)">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>{{user.name}}</strong>
                        <small class="text-muted d-block">{{ '@' + user.username }} • {{user.email}}</small>
                    </div>
                </div>
            </a>
            }
            @if(listData().length === 0 && (queryControl.value?.length ?? 0) >= 2){
            <div class="list-group-item text-center py-3 text-muted">
                No results found for "{{queryControl.value}}"
            </div>
            }
            }
        </div>
        }

        <div class="col-12 d-flex align-items-center justify-content-center gap-4 rounded-bottom   px-2 py-1"
            role="group">
            <button type="button" class="btn btn-secondary">Google Search</button>
            <button type="button" class="btn btn-secondary">I'm Feeling Lucky</button>
        </div>
    </form>
</div>