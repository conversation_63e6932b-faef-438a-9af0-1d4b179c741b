import { Routes } from '@angular/router';
import { Search } from './components/search/search';

export const routes: Routes = [

    {
        path: '',
        redirectTo: 'search',
        pathMatch: 'full'
    },
    {
        path: 'search',
        component: Search

    },
    {
        path: 'user/:id',
        loadComponent: () => import('./components/user-profile/user-profile').then(c => c.UserProfile)
    },
    {
        path: 'about',
        loadComponent: () => import('./components/about/about').then(c => c.About)
    },
    {
        path: '**',
        loadComponent: () => import('./components/not-found/not-found').then(c => c.NotFound)
    }
];
