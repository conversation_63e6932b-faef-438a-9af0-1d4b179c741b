<!-- <h1>{{ title() }}12</h1>
<p>compute 2**2 = {{ 2**4 }} </p>
<p>myObject.name = {{ name() }} </p>
<p> {{ 'name' in myObject? myObject['name']:'no-name-found' }} </p>
<ul>
  @for(item of items;track item.id; let i = $index){
  <li>{{ item.name }}: {{ i + 1 }}</li>
  }
</ul>
<button (click)="updateName()">Update Name</button>
<div appHighlight> color yellow</div>
<div [appHighlight]="'green'"> color green </div>
<ng-template #bannerContainer></ng-template>
<router-outlet></router-outlet> -->
<!-- <app-banner1></app-banner1>
<app-banner2></app-banner2>
<app-banner3></app-banner3>

<div appMycolor>my color</div>
<div [appHighlight]="'green'">2</div>
<div [appMycolor]="'blue'">my color blue</div> -->
<header class="text-center">
  <h1>Search Engine</h1>
</header>
<main>

  <router-outlet></router-outlet>
</main>
<footer class="text-center">
  Copyright © 2022 All rights are reserved.</footer>