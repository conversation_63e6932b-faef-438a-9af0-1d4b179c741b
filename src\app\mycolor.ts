/* eslint-disable @angular-eslint/no-input-rename */
import { Directive, ElementRef, HostListener, inject, input } from '@angular/core';

@Directive({
  selector: '[appMycolor]'
})
export class Mycolor {
  private readonly el = inject(ElementRef);
  appMycolor = input('', { alias: 'appMycolor' });


  constructor() {
    this.el.nativeElement.style.backgroundColor = this.appMycolor() || 'red';
  }

  @HostListener('mouseenter') onMouseEnter() {
    this.highlight(this.appMycolor());
  }

  @HostListener('mouseleave') onMouseLeave() {
    this.highlight('');
  }

  private highlight(color: string) {
    this.el.nativeElement.style.backgroundColor = color;
  }

}
