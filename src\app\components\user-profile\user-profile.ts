import { Component, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { Banner2 } from "@components/banner2/banner2";


@Component({
  selector: 'app-user-profile',
  imports: [RouterLink, Banner2],
  templateUrl: './user-profile.html',
  styleUrl: './user-profile.css'
})
export class UserProfile {

  private readonly activeRoute = inject(ActivatedRoute);

  constructor() {
    let id = this.activeRoute.snapshot.paramMap.get('id');
    console.log('new user id is ', id);

    this.activeRoute.params.pipe(
      takeUntilDestroyed()
    )
      .subscribe(params => {
        console.log('path', params);
      });

    this.activeRoute.queryParams.pipe(
      takeUntilDestroyed()
    )
      .subscribe(params => {
        console.log('query', params);
      });
  }



}
