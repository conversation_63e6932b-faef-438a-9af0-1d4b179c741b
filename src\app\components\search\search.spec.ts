import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { Router } from '@angular/router';

import { Search } from './search';

describe('Search', () => {
  let component: Search;
  let fixture: ComponentFixture<Search>;
  let routerSpy: jasmine.SpyObj<Router>;

  beforeEach(async () => {
    const spy = jasmine.createSpyObj('Router', ['navigate']);

    await TestBed.configureTestingModule({
      imports: [Search],
      providers: [
        { provide: Router, useValue: spy }
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(Search);
    component = fixture.componentInstance;
    routerSpy = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Form Validation', () => {
    it('should initialize with empty form', () => {
      expect(component.queryControl.value).toBe('');
      expect(component.queryControl.valid).toBeFalsy();
    });

    it('should be invalid when input is less than 3 characters', () => {
      component.queryControl.setValue('ab');
      expect(component.queryControl.invalid).toBeTruthy();
      expect(component.queryControl.errors?.['minlength']).toBeTruthy();
    });

    it('should be invalid when input is more than 10 characters', () => {
      component.queryControl.setValue('this is too long');
      expect(component.queryControl.invalid).toBeTruthy();
      expect(component.queryControl.errors?.['maxlength']).toBeTruthy();
    });

    it('should be valid when input is between 3 and 10 characters', () => {
      component.queryControl.setValue('john');
      expect(component.queryControl.valid).toBeTruthy();
    });

    it('should mark control as touched when value changes', fakeAsync(() => {
      expect(component.queryControl.touched).toBeFalsy();

      component.queryControl.setValue('test');
      tick();

      expect(component.queryControl.touched).toBeTruthy();
    }));
  });

  describe('Search Functionality', () => {
    it('should perform search when input has 2 or more characters', fakeAsync(() => {
      spyOn(component, 'performSearch' as any);

      component.queryControl.setValue('jo');
      tick(300); // Wait for debounce

      expect((component as any).performSearch).toHaveBeenCalledWith('jo');
    }));

    it('should not perform search when input has less than 2 characters', fakeAsync(() => {
      spyOn(component, 'performSearch' as any);

      component.queryControl.setValue('j');
      tick(300);

      expect((component as any).performSearch).not.toHaveBeenCalled();
    }));

    it('should filter users based on search term', fakeAsync(() => {
      component.queryControl.setValue('john');
      tick(500); // Wait for debounce + API simulation

      const results = component.listData();
      expect(results.length).toBeGreaterThan(0);
      expect(results.some(user =>
        user.name.toLowerCase().includes('john') ||
        user.username.toLowerCase().includes('john') ||
        user.email.toLowerCase().includes('john')
      )).toBeTruthy();
    }));

    it('should set loading state during search', fakeAsync(() => {
      expect(component.isLoading()).toBeFalsy();

      component.queryControl.setValue('john');
      tick(300); // Wait for debounce

      expect(component.isLoading()).toBeTruthy();

      tick(200); // Wait for API simulation

      expect(component.isLoading()).toBeFalsy();
    }));

    it('should clear results when search term is empty', fakeAsync(() => {
      // First set some data
      component.queryControl.setValue('john');
      tick(500);
      expect(component.listData().length).toBeGreaterThan(0);

      // Then clear
      component.queryControl.setValue('');
      tick(300);

      expect(component.listData().length).toBe(0);
    }));
  });

  describe('User Interactions', () => {
    it('should clear form and results when clear() is called', () => {
      component.queryControl.setValue('test');
      component.listData.set([{ id: 1, name: 'Test', username: 'test', email: '<EMAIL>' }]);

      component.clear();

      expect(component.queryControl.value).toBe('');
      expect(component.listData().length).toBe(0);
    });

    it('should navigate to user profile when navigateToProfile is called', () => {
      component.navigateToProfile(123);

      expect(routerSpy.navigate).toHaveBeenCalledWith(['/user', 123]);
    });

    it('should navigate to user profile with query params when navigateToProfileWithQueryParams is called', () => {
      component.navigateToProfileWithQueryParams(123);

      expect(routerSpy.navigate).toHaveBeenCalledWith(['/user', 123], { queryParams: { name: 'John' } });
    });

    it('should log message when takePhoto is called', () => {
      spyOn(console, 'log');

      component.takePhoto();

      expect(console.log).toHaveBeenCalledWith('Take photo functionality not implemented yet');
    });

    it('should log message when recordVoice is called', () => {
      spyOn(console, 'log');

      component.recordVoice();

      expect(console.log).toHaveBeenCalledWith('Voice recording functionality not implemented yet');
    });

    it('should perform search when search() method is called with valid input', () => {
      spyOn(component, 'performSearch' as any);

      component.queryControl.setValue('john');
      component.search();

      expect((component as any).performSearch).toHaveBeenCalledWith('john');
    });

    it('should not perform search when search() method is called with invalid input', () => {
      spyOn(component, 'performSearch' as any);

      component.queryControl.setValue('j');
      component.search();

      expect((component as any).performSearch).not.toHaveBeenCalled();
    });
  });

  describe('DOM Interactions', () => {
    it('should display validation error when input is too short', () => {
      const input = fixture.debugElement.nativeElement.querySelector('input[formControlName="query"]');

      input.value = 'ab';
      input.dispatchEvent(new Event('input'));
      component.queryControl.markAsTouched();
      fixture.detectChanges();

      const errorAlert = fixture.debugElement.nativeElement.querySelector('.alert-danger');
      expect(errorAlert).toBeTruthy();
      expect(errorAlert.textContent).toContain('Minimum length is 3 characters');
    });

    it('should display validation error when input is too long', () => {
      const input = fixture.debugElement.nativeElement.querySelector('input[formControlName="query"]');

      input.value = 'this is way too long';
      input.dispatchEvent(new Event('input'));
      component.queryControl.markAsTouched();
      fixture.detectChanges();

      const errorAlert = fixture.debugElement.nativeElement.querySelector('.alert-danger');
      expect(errorAlert).toBeTruthy();
      expect(errorAlert.textContent).toContain('Maximum length is 10 characters');
    });

    it('should not display validation error when input is valid', () => {
      const input = fixture.debugElement.nativeElement.querySelector('input[formControlName="query"]');

      input.value = 'john';
      input.dispatchEvent(new Event('input'));
      component.queryControl.markAsTouched();
      fixture.detectChanges();

      const errorAlert = fixture.debugElement.nativeElement.querySelector('.alert-danger');
      expect(errorAlert).toBeFalsy();
    });

    it('should display loading spinner when searching', fakeAsync(() => {
      component.queryControl.setValue('john');
      tick(300); // Wait for debounce
      fixture.detectChanges();

      const spinner = fixture.debugElement.nativeElement.querySelector('.spinner-border');
      expect(spinner).toBeTruthy();
      expect(spinner.parentElement.textContent.trim()).toContain('Searching...');

      tick(200); // Wait for API simulation
      fixture.detectChanges();

      const spinnerAfter = fixture.debugElement.nativeElement.querySelector('.spinner-border');
      expect(spinnerAfter).toBeFalsy();
    }));

    it('should display search results', fakeAsync(() => {
      component.queryControl.setValue('john');
      tick(500); // Wait for debounce + API simulation
      fixture.detectChanges();

      const listItems = fixture.debugElement.nativeElement.querySelectorAll('.list-group-item-action');
      expect(listItems.length).toBeGreaterThan(0);
    }));

    it('should display "No results found" message when no matches', fakeAsync(() => {
      component.queryControl.setValue('xyz');
      tick(500); // Wait for debounce + API simulation
      fixture.detectChanges();

      const noResultsMessage = fixture.debugElement.nativeElement.querySelector('.text-muted');
      expect(noResultsMessage.textContent).toContain('No results found for "xyz"');
    }));

    it('should clear input when clear button is clicked', () => {
      component.queryControl.setValue('test');
      fixture.detectChanges();

      const clearButton = fixture.debugElement.nativeElement.querySelector('a[role="button"]');
      clearButton.click();

      expect(component.queryControl.value).toBe('');
      expect(component.listData().length).toBe(0);
    });
  });
});
