import { ChangeDetectionStrategy, Component, ComponentRef, effect, OnInit, signal, ViewChild, ViewContainerRef } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { Observable, Subscription } from 'rxjs';

import { Highlight } from '@cores/highlight';
import { Mycolor } from './mycolor';
import { Banner1 } from '@components/banner1/banner1';
import { Banner2 } from '@components/banner2/banner2';
import { Banner3 } from '@components/banner3/banner3';


@Component({
  selector: 'app-root',
  imports: [RouterOutlet, Banner1, Banner2, Banner3, Highlight, Mycolor],
  standalone: true,
  providers: [],
  templateUrl: './app.html',
  styleUrl: './app.css',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class App implements OnInit {
  // @ViewChild('bannerContainer', { read: ViewContainerRef, static: true }) container!: ViewContainerRef;
  protected readonly title = signal('ng20-demo2');
  private readonly banners = [Banner1, Banner2, Banner3];
  private currentIndex = 0;
  private currentComponent?: ComponentRef<any>;
  protected readonly name = signal('John');
  protected readonly myObject = { name: "John", age: 30, city: "New York" };
  protected readonly items = [
    { id: 1, name: 'Item 1' },
    { id: 2, name: 'Item 2' },
    { id: 3, name: 'Item 3' }
  ];
  protected readonly myObservable$ = new Observable<string>((observer) => {
    observer.next('Hello');
    observer.next('World');
    observer.next('!');
    observer.complete();
  });

  // constructor() {
  //   effect(() => {
  //     console.log('name changed to ' + this.name());
  //   });
  //   effect((onCleanup) => {
  //     const sub: Subscription = this.myObservable$.subscribe(value => {
  //       console.log('myObservable changed to ' + value);
  //     });
  //     onCleanup(() => {
  //       sub.unsubscribe();
  //     });
  //   });
  // }

  ngOnInit() {
    // this.loadBanner();
    // setInterval(() => {
    //   this.loadBanner();
    // }, 2000);
  }

  updateName() {
    // this.name.set('Jane');
    let random = Math.random();
    this.name.update(name => name + random.toString());
  }

  greet(content: string) {
    return 'Hello ' + content;
  }

  // private loadBanner() {
  //   if (this.currentComponent) {
  //     this.currentComponent.destroy();
  //   }
  //   const banner = this.banners[this.currentIndex];
  //   this.currentComponent = this.container.createComponent(banner);
  //   this.currentComponent.instance.title = 'banner' + (this.currentIndex + 1);

  //   this.currentIndex = (this.currentIndex + 1) % this.banners.length;
  // }
}
